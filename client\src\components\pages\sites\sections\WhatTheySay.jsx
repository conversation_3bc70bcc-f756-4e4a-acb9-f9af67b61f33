"use client";

import { Ava<PERSON>, Container, Grid } from "@mui/material";
import { useEffect, useRef, useState } from "react";

import ArrowLeft from "@/assets/images/icons/arrow-left.svg";
import ArrowRight from "@/assets/images/icons/arrow-right.svg";
import GuillemetIcon from "@/assets/images/icons/guillemet.svg";
import StarRating from "@/components/ui/StarRating";
import { feedbacks } from "@/utils/constants";

function WhatTheySay() {
  const saySectionRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(null);

  const scrollByItem = (direction) => {
    if (!saySectionRef.current) return;

    const container = saySectionRef.current;

    const item = container.firstChild;
    if (!item) return;

    const itemWidth = item.offsetWidth + 40;
    const maxScrollLeft = container.scrollWidth - container.clientWidth;

    let newScrollLeft =
      container.scrollLeft + (direction === "left" ? -itemWidth : itemWidth);

    newScrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft));

    container.scrollTo({
      left: newScrollLeft,
      behavior: "smooth",
    });
  };

  const scrollLeft = () => scrollByItem("left");
  const scrollRight = () => scrollByItem("right");

  useEffect(() => {
    const saySection = saySectionRef.current;
    if (!saySection) return;

    const handleScroll = () => {
      const sectionRect = saySection.getBoundingClientRect();
      const sectionCenter = sectionRect.left + sectionRect.width / 2;
      let closestIndex = null;
      let minDistance = Infinity;

      saySection.childNodes.forEach((item, index) => {
        const itemRect = item.getBoundingClientRect();
        const itemCenter = itemRect.left + itemRect.width / 2;
        const distance = Math.abs(sectionCenter - itemCenter);

        if (distance < minDistance) {
          minDistance = distance;
          closestIndex = index;
        }
      });

      setActiveIndex(closestIndex);
    };

    saySection.addEventListener("scroll", handleScroll);
    handleScroll();

    return () => {
      saySection.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <div id="what-they-say">
      <Container className="custom-max-width">
        <Grid className="container" container spacing={2}>
          <Grid item xs={12} sm={12}>
            <h2 className="heading-h1 text-white text-center">What they say</h2>
          </Grid>
          <div className="say-section" ref={saySectionRef}>
            {feedbacks.map((feedback, index) => (
              <div
                className={`say-item ${index === activeIndex ? "scale-up" : "scale-down"}`}
                key={feedback.id}
              >
                <div className="guillemet">
                  <GuillemetIcon />
                </div>
                <p className="say-content">{feedback.description}</p>
                <StarRating rating={feedback.rating} />
                <div className="author">
                  <Avatar sx={{ width: 70, height: 70 }} src={feedback.image}>
                    <span className="author-icon">
                      {feedback.author.split(" ")[0][0].toUpperCase()}
                    </span>
                  </Avatar>
                  <div className="info-author">
                    <p className="name">{feedback.author}</p>
                    <p className="quality">{feedback.quality}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="buttons">
            <button className="scroll-button left" aria-label="ArrowLeft" onClick={scrollLeft}>
              <ArrowLeft />
            </button>
            <button className="scroll-button right" aria-label="ArrowRight" onClick={scrollRight}>
              <ArrowRight />
            </button>
          </div>
        </Grid>
      </Container>
    </div>
  );
}

export default WhatTheySay;
