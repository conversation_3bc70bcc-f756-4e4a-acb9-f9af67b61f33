import HttpException from '@/utils/exceptions/http.exception';
import guideModel from './guide.model';
import { GuideI, GuideVersion, Language } from './guide.interface';
import { UserI } from '../user/user.interfaces';
import { downloadModel } from './downloadguide/downloadguide.model';
import mongoose, { FilterQuery, Types } from 'mongoose';
import { Role, Visibility } from '@/utils/helpers/constants';
import { DownloadQuery } from 'types/request/DownloadQuery';
import FilesService from '../storage/files.service';
import { sendEmail } from '@/utils/services';
import { getCategoryGuideUrlByIdVersion, getCategoryUrlByIdVersion, getServiceGuideUrlByIdVersion } from '@/utils/helpers/functions';
import articleModel from '../article/article.model';
import articleCategoryModel from '../article/category/article.category.model';
import { MESSAGES } from '@/utils/helpers/messages';
class GuideService {
    private readonly filesService = new FilesService();

    public async createGuide(guideData: any): Promise<any> {
        const existingUrls: Set<string> = new Set();
        const existingGuides = await guideModel.find({}, 'versionsguide.url');
        existingGuides.forEach(existingGuide => {
            existingGuide.versionsguide?.forEach(version => {
                existingUrls.add(version.url);
            });
        });

        const defaultLanguage: Language = Language.ENGLISH;

        if (!Array.isArray(guideData.versionsguide)) {
            guideData.versionsguide = [];
        }

        guideData.versionsguide.forEach((version: any) => {
            if (!version.language) {
                version.language = defaultLanguage;
            }
            let url = version.url || version.title.toLowerCase().replace(/\s+/g, '-');
            if (existingUrls.has(url)) {
                throw new HttpException(409, MESSAGES.GUIDE.URL_ALREADY_EXIST);
            }
        });

        const newGuide = new guideModel({
            ...guideData,
            versionsguide: guideData.versionsguide,
        });
        const savedGuide = await newGuide.save();
        return savedGuide;
    }

    public async getSlugBySlug(language: string, url: string): Promise<{ slug: string }> {
        const guide = await guideModel.findOne({ 'versionsguide.language': language, 'versionsguide.url': url.toLocaleLowerCase() });
        if (!guide) throw new HttpException(404, MESSAGES.GUIDE.NOT_FOUND);

        const targetLanguage = language === 'en' ? 'fr' : 'en';
        const selectedVersions = guide.versionsguide.filter(version => version.language === targetLanguage);

        if (selectedVersions.length === 0) throw new HttpException(404, MESSAGES.GUIDE.VERSION_NOT_EXIST);

        return {
            slug: selectedVersions[0].url,
        };
    }

    public async downloadGuide(guideId: string, guideData: any): Promise<void> {
        const guideObjectId = new mongoose.Types.ObjectId(guideId);
        const guide = await guideModel.findById(guideObjectId);
        if (!guide) throw new HttpException(404, MESSAGES.GUIDE.NOT_FOUND);

        if (!guideData.firstName || !guideData.lastName) {
            throw new HttpException(400, MESSAGES.GUIDE.MISSING_EMAIL_OR_FIRSTNAME);
        }

        let existingDownload = await downloadModel.findOne({ email: guideData.email });

        let isNewDownload = false;

        if (existingDownload) {
            if (!existingDownload.guides.includes(guideObjectId)) {
                await downloadModel.updateOne(
                    { email: guideData.email },
                    {
                        $addToSet: { guides: guideObjectId },
                        $set: {
                            companysize: existingDownload.companysize || guideData.companysize,
                            Headquarters: existingDownload.Headquarters || guideData.Headquarters,
                        },
                    },
                );
                isNewDownload = true;
            }
        } else {
            await downloadModel.create({
                email: guideData.email,
                firstName: guideData.firstName,
                lastName: guideData.lastName,
                companysize: guideData.companysize,
                Headquarters: guideData.Headquarters,
                guides: [guideObjectId],
                user: null,
            });
            isNewDownload = true;
        }

        if (isNewDownload) {
            await guideModel.findByIdAndUpdate(guide._id, { $inc: { totalDownload: 1 } });
        }

        const filePath = await this.filesService.findFile(guide?.versionsguide[0]?.file);
        const downloadUrl = `https://www.pentabell.com/api/v1/files/${guide?.versionsguide[0]?.file}`;

        await sendEmail({
            to: guideData.email,
            subject: 'Your Global Payroll Checklist is Ready! ✅',
            template: 'downloadGuide',
            context: {
                firstName: guideData.firstName,
                lastName: guideData.lastName,
                downloadUrl: downloadUrl,
                titleGuide: guide?.versionsguide[0]?.title,
                image: guide?.mailBanner || null,
            },
            attachments: [
                {
                    filename: `Guide_${guide?.versionsguide[0]?.title}.pdf`,
                    path: filePath,
                    contentType: 'application/pdf',
                },
            ],
        });
    }

    public async getGuideByLanguageAndId(language: string, id: string): Promise<any> {
        const guides = await guideModel
            .aggregate([
                { $match: { _id: mongoose.Types.ObjectId.createFromHexString(id) } },
                { $unwind: '$versionsguide' },
                { $match: { 'versionsguide.language': language } },
                { $unwind: { path: '$versionsguide.category', preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: 'categoryguides',
                        let: { categoryId: '$versionsguide.category' },
                        pipeline: [
                            { $unwind: '$categoryguide' },
                            { $match: { $expr: { $eq: ['$categoryguide._id', '$$categoryId'] } } },
                            {
                                $project: {
                                    id: '$categoryguide._id',
                                    name: '$categoryguide.name',
                                    url: '$categoryguide.url',
                                },
                            },
                        ],
                        as: 'categoryGuideDetails',
                    },
                },
                { $unwind: { path: '$categoryGuideDetails', preserveNullAndEmptyArrays: true } },
                { $unwind: { path: '$versionsguide.service', preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: 'categories',
                        let: { categoryServiceId: '$versionsguide.service' },
                        pipeline: [
                            { $unwind: '$versionscategory' },
                            { $match: { $expr: { $eq: ['$versionscategory._id', '$$categoryServiceId'] } } },
                            {
                                $project: {
                                    id: '$versionscategory._id',
                                    name: '$versionscategory.name',
                                    url: '$versionscategory.url',
                                },
                            },
                        ],
                        as: 'categoryServiceDetails',
                    },
                },
                { $unwind: { path: '$categoryServiceDetails', preserveNullAndEmptyArrays: true } },
                {
                    $group: {
                        _id: {
                            id: '$_id',
                            language: '$versionsguide.language',
                            url: '$versionsguide.url',
                            title: '$versionsguide.title',

                            file: '$versionsguide.file',
                            image: '$versionsguide.image',
                            mailBanner: '$mailBanner',
                            keywords: '$versionsguide.keywords',
                            visibility: '$versionsguide.visibility',
                            metaTitle: '$versionsguide.metaTitle',
                            metaDescription: '$versionsguide.metaDescription',
                            alt: '$versionsguide.alt',
                            content: '$versionsguide.content',
                            publishDate: '$versionsguide.publishDate',
                            highlights: '$versionsguide.highlights',
                            description: '$versionsguide.description',
                            guideList: '$versionsguide.guideList',
                            createdAt: '$versionsguide.createdAt',
                            updatedAt: '$versionsguide.updatedAt',
                            isArchived: '$versionsguide.isArchived',
                            robotsMeta: '$robotsMeta',
                            cible: '$cible',
                        },
                        categoryguides: {
                            $addToSet: {
                                id: '$categoryGuideDetails.id',
                                name: '$categoryGuideDetails.name',
                                url: '$categoryGuideDetails.url',
                            },
                        },
                        categories: {
                            $addToSet: {
                                id: '$categoryServiceDetails.id',
                                name: '$categoryServiceDetails.name',
                                url: '$categoryServiceDetails.url',
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: '$_id.id',
                        versionsguide: {
                            language: '$_id.language',
                            title: '$_id.title',
                            file: '$_id.file',
                            url: '$_id.url',
                            keywords: '$_id.keywords',
                            metaTitle: '$_id.metaTitle',
                            metaDescription: '$_id.metaDescription',
                            alt: '$_id.alt',

                            content: '$_id.content',
                            publishDate: '$_id.publishDate',
                            highlights: '$_id.highlights',
                            description: '$_id.description',
                            image: '$_id.image',
                            mailBanner: '$_id.mailBanner',
                            visibility: '$_id.visibility',
                            guideList: '$_id.guideList',
                            isArchived: '$_id.isArchived',
                            categoryguides: {
                                $cond: {
                                    if: { $eq: ['$categoryguides', [null]] },
                                    then: [],
                                    else: '$categoryguides',
                                },
                            },
                            categories: {
                                $cond: {
                                    if: { $eq: ['$categories', [null]] },
                                    then: [],
                                    else: '$categories',
                                },
                            },
                        },
                        robotsMeta: '$_id.robotsMeta',
                        cible: '$_id.cible',
                    },
                },
            ])
            .exec();

        if (guides.length === 0) throw new HttpException(404, MESSAGES.GUIDE.NOT_FOUND);

        return guides[0];
    }

    public async updateMainfieldsGuide(guideId: string, updateData: any): Promise<any> {
        const guideToUpdate = await guideModel.findById(guideId).lean();

        if (!guideToUpdate) throw new HttpException(404, 'Guide not found.');

        updateData.versions = undefined;

        const updateGuide = await guideModel.findByIdAndUpdate(guideId, { $set: updateData }, { new: true });

        return updateGuide?.toObject();
    }

    public async getGuideByUrl(language: string, url: string, currentUser: UserI): Promise<any> {
        const guides = await guideModel
            .aggregate([
                { $match: { 'versionsguide.language': language, 'versionsguide.url': url.toLocaleLowerCase() } },
                { $unwind: '$versionsguide' },
                { $match: { 'versionsguide.language': language, 'versionsguide.url': url.toLocaleLowerCase() } },
                { $unwind: { path: '$versionsguide.category', preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: 'categoryguides',
                        let: { categoryId: '$versionsguide.category' },
                        pipeline: [
                            { $unwind: '$categoryguide' },
                            { $match: { $expr: { $eq: ['$categoryguide._id', '$$categoryId'] } } },
                            {
                                $project: {
                                    id: '$categoryguide._id',
                                    name: '$categoryguide.name',
                                    url: '$categoryguide.url',
                                },
                            },
                        ],
                        as: 'categoryGuideDetails',
                    },
                },
                { $unwind: { path: '$categoryGuideDetails', preserveNullAndEmptyArrays: true } },
                { $unwind: { path: '$versionsguide.service', preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: 'categories',
                        let: { categoryServiceId: '$versionsguide.service' },
                        pipeline: [
                            { $unwind: '$versionscategory' },
                            { $match: { $expr: { $eq: ['$versionscategory._id', '$$categoryServiceId'] } } },
                            {
                                $project: {
                                    id: '$versionscategory._id',
                                    name: '$versionscategory.name',
                                    url: '$versionscategory.url',
                                },
                            },
                        ],
                        as: 'categoryServiceDetails',
                    },
                },
                { $unwind: { path: '$categoryServiceDetails', preserveNullAndEmptyArrays: true } },
                {
                    $group: {
                        _id: {
                            id: '$_id',
                            idguide: '$versionsguide._id',
                            language: '$versionsguide.language',
                            url: '$versionsguide.url',
                            title: '$versionsguide.title',
                            description: '$versionsguide.description',
                            keywords: '$versionsguide.keywords',
                            metaTitle: '$versionsguide.metaTitle',
                            metaDescription: '$versionsguide.metaDescription',
                            alt: '$versionsguide.alt',
                            content: '$versionsguide.content',
                            image: '$versionsguide.image',
                            mailBanner: '$mailBanner',
                            file: '$versionsguide.file',
                            tags: '$tags',
                            cible: '$cible',
                            visibility: `$versionsguide.visibility`,
                            isArchived: `$versionsguide.isArchived`,
                            guideList: '$versionsguide.guideList',
                            publishDate: '$versionsguide.publishDate',
                            highlights: '$versionsguide.highlights',
                            robotsMeta: '$robotsMeta',
                            createdAt: '$createdAt',
                            updatedAt: '$updatedAt',
                        },
                        categoryguides: {
                            $addToSet: {
                                id: '$categoryGuideDetails.id',
                                name: '$categoryGuideDetails.name',
                                url: '$categoryGuideDetails.url',
                            },
                        },
                        categories: {
                            $addToSet: {
                                id: '$categoryServiceDetails.id',
                                name: '$categoryServiceDetails.name',
                                url: '$categoryServiceDetails.url',
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: '$_id.id',
                        versionsguide: {
                            language: '$_id.language',
                            guideId: '$_id.guideId',
                            title: '$_id.title',
                            description: '$_id.description',
                            keywords: '$_id.keywords',
                            highlights: '$_id.highlights',
                            metaTitle: '$_id.metaTitle',
                            metaDescription: '$_id.metaDescription',
                            visibility: `$_id.visibility`,
                            isArchived: `$_id.isArchived`,
                            publishDate: '$_id.publishDate',
                            updatedAt: '$_id.updatedAt',
                            content: '$_id.content',
                            url: '$_id.url',
                            alt: '$_id.alt',
                            image: '$_id.image',
                            mailBanner: '$_id.mailBanner',
                            guideList: '$_id.guideList',
                            categoryguides: {
                                $cond: {
                                    if: { $eq: ['$categoryguides', [null]] },
                                    then: [],
                                    else: '$categoryguides',
                                },
                            },
                            categories: {
                                $cond: {
                                    if: { $eq: ['$categories', [null]] },
                                    then: [],
                                    else: '$categories',
                                },
                            },
                            file: '$_id.file',
                        },

                        robotsMeta: '$_id.robotsMeta',
                        createdAt: '$_id.createdAt',
                        updatedAt: '$_id.updatedAt',
                        cible: '$_id.cible',
                    },
                },
            ])
            .exec();

        if (guides.length === 0) throw new HttpException(404, MESSAGES.GUIDE.NOT_FOUND);

        const guide = guides[0];

        if (
            (guide.versionsguide.visibility !== 'Public' && (!currentUser || currentUser?.roles?.includes(Role.CANDIDATE))) ||
            guide.versionsguide.isArchived === true
        )
            throw new HttpException(404, MESSAGES.GUIDE.NOT_FOUND);

        return {
            _id: guide._id,
            versionsguide: [guide.versionsguide],
            robotsMeta: guide.robotsMeta || '',
            cible: guide.cible || '',
            createdAt: guide.createdAt,
            updatedAt: guide.updatedAt,
        };
    }

    public async getGuides(queries: any, language: Language): Promise<any> {
        const { paginated = 'true', searchQuery, sortOrder = 'desc', visibility, dashboard, publishDate, createdAt, cible } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 5;
        const queryConditions: any = {};

        if (!dashboard) queryConditions['versionsguide.isArchived'] = false;

        if (createdAt) {
            const date = new Date(createdAt);
            queryConditions['createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }

        if (searchQuery) {
            const queryCondition = [
                { 'versionsguide.title': new RegExp(`.*${searchQuery}.*`, 'i') },
                { 'versionsguide.keywords': { $in: [searchQuery] } },
            ];
            const linkCondition = searchQuery.includes('https://') ? [{ 'versionsguide.content': RegExp(searchQuery) }] : queryCondition;
            queryConditions['$or'] = linkCondition;
        }

        if (language) queryConditions['versionsguide.language'] = language;
        if (visibility) queryConditions['versionsguide.visibility'] = visibility;
        if (cible) queryConditions['cible'] = cible;
        if (publishDate) {
            const date = new Date(publishDate);
            queryConditions['versionsguide.createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }

        const sortCriteria: any = { 'versionsguide.createdAt': sortOrder === 'asc' ? 1 : -1 };
        const skip = (pageNumber - 1) * pageSize;

        try {
            const guides = await guideModel.find(queryConditions).sort(sortCriteria).skip(skip).limit(pageSize).lean();

            if (!guides || guides.length === 0) {
                return {
                    totalGuides: 0,
                    guides: [],
                };
            }

            const filteredGuides: any = await Promise.all(
                guides.map(async guide => {
                    const filteredVersions = guide.versionsguide.filter(
                        (version: GuideVersion) => version.language === language && (!visibility || version.visibility === visibility),
                    );

                    if (!filteredVersions || filteredVersions.length === 0) {
                        return null;
                    }

                    const category = await getCategoryGuideUrlByIdVersion(filteredVersions[0]?.category?.[0]);
                    const service = await getServiceGuideUrlByIdVersion(filteredVersions[0]?.service?.[0]);

                    return {
                        ...guide,
                        versions: filteredVersions,
                        existingLanguages: Array.from(new Set(guide.versionsguide.map((version: GuideVersion) => version.language))),
                        category,
                        service,
                    };
                }),
            );

            if (paginated === 'false') {
                return filteredGuides;
            }

            const totalGuides = await guideModel.countDocuments(queryConditions);
            const totalPages = Math.ceil(totalGuides / pageSize);

            return {
                pageNumber,
                pageSize,
                totalPages,
                totalGuides,
                guides: filteredGuides,
            };
        } catch (error: any) {
            console.log('error', error);
        }
    }

    public async getArticlesAndGuides(queries: any, language: Language): Promise<any> {
        const { searchQuery, sortOrder = 'desc', publishDate, createdAt, categoryName } = queries;

        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 9;

        const articleQueryConditions: any = {};
        const guideQueryConditions: any = { isArticle: true };

        if (categoryName) {
            const category: any = await articleCategoryModel.findOne({ [`versions.${language}.name`]: categoryName }).exec();
            if (!category) throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);
            const categoryVersion = category.versions.get ? category.versions.get(language) : category.versions[language];
            if (!categoryVersion) throw new HttpException(404, MESSAGES.CATEGORY.VERSION_NOT_EXIST);
            articleQueryConditions['versions._id'] = { $in: categoryVersion.articles };
        }

        if (createdAt) {
            const date = new Date(createdAt);
            const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
            const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999);
            articleQueryConditions['createdAt'] = { $gte: startOfDay, $lte: endOfDay };
            guideQueryConditions['createdAt'] = { $gte: startOfDay, $lte: endOfDay };
        }

        if (searchQuery) {
            const regex = new RegExp(`.*${searchQuery}.*`, 'i');
            articleQueryConditions['versions'] = {
                $elemMatch: {
                    language,
                    visibility: 'Public',
                    isArchived: false,
                    $or: [{ title: regex }, { keywords: { $in: [searchQuery] } }],
                },
            };
            guideQueryConditions['versionsguide'] = {
                $elemMatch: {
                    language,
                    visibility: 'Public',
                    isArchived: false,
                    $or: [{ title: regex }, { keywords: { $in: [searchQuery] } }],
                },
            };
        } else {
            articleQueryConditions['versions.language'] = language;
            articleQueryConditions['versions.visibility'] = 'Public';
            articleQueryConditions['versions.isArchived'] = false;
            guideQueryConditions['versionsguide.language'] = language;
            guideQueryConditions['versionsguide.visibility'] = 'Public';
            guideQueryConditions['versionsguide.isArchived'] = false;
        }

        if (publishDate) {
            const date = new Date(publishDate);
            const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
            const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999);
            articleQueryConditions['publishDate'] = { $gte: startOfDay, $lte: endOfDay };
        }

        const skip = (pageNumber - 1) * pageSize;

        const articles = await articleModel.find(articleQueryConditions).lean();
        const filteredArticles = await Promise.all(
            articles.map(async article => {
                const versions = article.versions.filter(v => v.language === language && v.visibility === Visibility.Public);
                if (!versions.length) return null;
                const category = await getCategoryUrlByIdVersion(versions[0]?.category?.[0]);
                return {
                    ...article,
                    type: 'article',
                    versions,
                    category,
                };
            }),
        );

        const guides = await guideModel.find(guideQueryConditions).lean();
        const filteredGuides = await Promise.all(
            guides.map(async guide => {
                const versions = guide.versionsguide.filter(v => v.language === language);
                if (!versions.length) return null;
                const category = await getCategoryGuideUrlByIdVersion(versions[0]?.category?.[0]);
                const service = await getServiceGuideUrlByIdVersion(versions[0]?.service?.[0]);
                return {
                    ...guide,
                    type: 'guide',
                    versionsguide: versions,
                    category,
                    service,
                };
            }),
        );

        const validArticles = filteredArticles.filter(Boolean);
        const validGuides = filteredGuides.filter(Boolean);
        const sortedArticles = [...validArticles].sort((a, b) => {
            const getDate = (item: any) => {
                if (!item || !item.versions || !item.versions[0]) return 0;
                return new Date(item.versions[0].createdAt).getTime();
            };

            const dateA = getDate(a);
            const dateB = getDate(b);

            return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
        });

        const isFiltered = Boolean(searchQuery || categoryName || createdAt);

        let firstArticle: any = null;
        let combinedItems: any[] = [];

        if (!isFiltered && sortedArticles.length > 0) {
            firstArticle = sortedArticles[0];

            const firstId = firstArticle._id.toString();
            const articlesWithoutFirst = validArticles.filter(a => a && a._id?.toString() !== firstId);

            combinedItems = [...articlesWithoutFirst, ...validGuides];
        } else {
            combinedItems = [...validArticles, ...validGuides];
        }

        combinedItems.sort((a, b) => {
            const getCreatedAt = (item: any) => {
                if (item.type === 'article') return new Date(item.versions[0].createdAt).getTime();
                if (item.type === 'guide') return new Date(item.versionsguide[0].createdAt).getTime();
                return 0;
            };
            const dateA = getCreatedAt(a);
            const dateB = getCreatedAt(b);
            return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
        });

        const totalItems = combinedItems.length;
        const totalPages = Math.ceil(totalItems / pageSize);
        const paginatedItems = combinedItems.slice(skip, skip + pageSize);

        return {
            List: {
                pageNumber,
                pageSize,
                totalPages,
                totalItems: isFiltered ? validArticles.length + validGuides.length : totalItems + 1,
                firstArticle: !isFiltered ? firstArticle : null,
                items: paginatedItems,
            },
        };
    }

    public async addVersionToGuide(guideId: string, newVersion: any): Promise<any> {
        const existingguide = await guideModel.findById(guideId);
        if (!existingguide) throw new HttpException(404, MESSAGES.GUIDE.NOT_FOUND);

        const existingUrls: Set<string> = new Set();
        const existingguides = await guideModel.find({}, 'versionsguide');

        existingguides.forEach((guide: any) => {
            if (guide.versionsguide && Array.isArray(guide.versionsguide)) {
                guide.versionsguide.forEach((version: any) => {
                    if (version.url) {
                        existingUrls.add(version.url);
                    }
                });
            }
        });

        if (!newVersion.url) {
            newVersion.url = this.generateUniqueUrl(newVersion.title, existingUrls);
        } else {
            newVersion.url = this.generateUniqueUrl(newVersion.url, existingUrls);
        }

        newVersion.alt = newVersion.alt || newVersion.title;

        existingguide.versionsguide.push(newVersion);

        const updatedguide = await existingguide.save();
        return updatedguide;
    }

    private generateUniqueUrl(baseUrl: string, existingUrls: Set<string>): string {
        let tempUrl = baseUrl.toLowerCase().replace(/\s+/g, '-');
        let count = 1;

        while (existingUrls.has(tempUrl)) {
            tempUrl = `${baseUrl.toLowerCase().replace(/\s+/g, '-')}-${count}`;
            count++;
        }

        existingUrls.add(tempUrl);
        return tempUrl;
    }

    public async updateGuideByLanguageandId(language: Language, guideId: string, updateData: Partial<any>): Promise<any> {
        const guideToUpdate = await guideModel.findById(guideId);
        if (!guideToUpdate) throw new HttpException(404, MESSAGES.GUIDE.NOT_FOUND);

        const existingUrls: Set<string> = new Set();
        const existingguides = await guideModel.find({}, 'versionsguide');

        existingguides.forEach((existingguide: any) => {
            if (existingguide && Array.isArray(existingguide.versionsguide)) {
                existingguide.versionsguide.forEach((version: any) => {
                    if (version && version.url) {
                        existingUrls.add(version.url);
                    }
                });
            }
        });

        let updatedVersion: GuideVersion | null = null;

        for (const version of guideToUpdate.versionsguide) {
            if (version.language === language) {
                const titleChanged = updateData.title && updateData.title !== version.title;

                if (titleChanged) {
                    let newUrl = this.generateUniqueUrl(updateData.title || version.title, existingUrls);
                    version.url = newUrl;
                }

                Object.assign(version, updateData);

                if (titleChanged || updateData.alt) {
                    version.alt = updateData.alt || updateData.title;
                }

                updatedVersion = version;
                break;
            }
        }

        if (!updatedVersion) throw new HttpException(404, `No version found for language ${language} in guide with ID ${guideId}.`);

        await guideToUpdate.save();

        return guideToUpdate;
    }

    public async updateGuideVersion(guideId: string, language: Language, versionData: Partial<any>): Promise<any> {
        const existingguide = await guideModel.findById(guideId);
        if (!existingguide) throw new HttpException(404, MESSAGES.GUIDE.NOT_FOUND);

        const existingUrls: Set<string> = new Set();
        const existingguides = await guideModel.find({ _id: { $ne: guideId } }, 'versionsguide');

        existingguides.forEach((guide: any) => {
            if (guide.versionsguide && Array.isArray(guide.versionsguide)) {
                guide.versionsguide.forEach((version: any) => {
                    if (version.url) {
                        existingUrls.add(version.url);
                    }
                });
            }
        });

        let isVersionExisting = false;

        for (const version of existingguide.versionsguide) {
            if (version.language === language) {
                isVersionExisting = true;
                break;
            }
        }

        if (isVersionExisting) {
            return await this.updateGuideByLanguageandId(language, guideId, versionData);
        } else {
            const newVersion: GuideVersion = {
                language,
                title: versionData.title,
                url: versionData.url || versionData.title.toLowerCase().replace(/\s+/g, '-'),
                alt: versionData.alt || versionData.title,
                content: versionData.content,
                metaTitle: versionData.metaTitle,
                metaDescription: versionData.metaDescription,
                image: versionData.image,
                category: versionData.category || [],
                service: versionData.service || [],
                keywords: versionData.keywords || '',
                file: versionData.file,
                _id: new Types.ObjectId(),
                createdAt: new Date(),
                updatedAt: new Date(),
                visibility: versionData.visibility,
                publishDate: versionData.publishDate,
                highlights: versionData.highlights || [],
                description: versionData.description || '',
                guideList: versionData.guideList || [],
                isArchived: false,
            };

            return await this.addVersionToGuide(guideId, newVersion);
        }
    }

    public async get(guideId: string): Promise<any> {
        const guide: GuideI | null = await guideModel.findById(guideId).lean();
        if (!guide) throw new HttpException(404, MESSAGES.GUIDE.NOT_FOUND);
        return guide;
    }

    public async getAllDownloadByGuides(guideId: string, queries: DownloadQuery): Promise<any> {
        const { paginated = 'true', pageNumber = 1, pageSize = 10, firstName, createdAt, sortOrder = 'desc' } = queries;

        const queryConditions: FilterQuery<any> = { guides: new Types.ObjectId(guideId) };

        const guide = await guideModel.findById(guideId);
        if (!guide) throw new HttpException(404, MESSAGES.GUIDE.NOT_FOUND);

        if (createdAt) {
            const date = new Date(createdAt);
            queryConditions['createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }

        if (firstName) queryConditions['firstName'] = new RegExp(`.*${firstName}.*`, 'i');

        console.log('Query Conditions:', JSON.stringify(queryConditions, null, 2));

        const sortCriteria: any = {};
        if (sortOrder) sortCriteria['createdAt'] = sortOrder === 'asc' ? 1 : -1;

        let downloadPaginated = await downloadModel.find(queryConditions).lean().sort(sortCriteria);

        const totalDownload = await downloadModel.countDocuments(queryConditions);

        if (paginated !== 'true') {
            return { totalDownload, downloads: downloadPaginated };
        }

        const paginatedDownloads = downloadPaginated.slice((pageNumber - 1) * pageSize, pageNumber * pageSize);
        const totalPages = Math.ceil(totalDownload / pageSize);

        return { totalDownload, pageNumber, pageSize, totalPages, Downloads: paginatedDownloads };
    }

    public async getGuideTitles(language: Language) {
        return guideModel.aggregate([
            { $unwind: '$versionsguide' },
            { $match: { 'versionsguide.language': language } },
            {
                $project: {
                    _id: 0,
                    title: '$versionsguide.title',
                    guideId: '$versionsguide._id',
                },
            },
        ]);
    }

    public async getOppositeLanguageVersionsGuide(language: string, versionIds: string[]): Promise<any[]> {
        const targetLanguage = language === 'en' ? 'fr' : 'en';

        const guides = await guideModel.find({ 'versionsguide._id': { $in: versionIds } }).exec();

        if (!guides || guides.length === 0) {
            return [];
        }

        const filtredGuides = guides.map(guide => {
            const targetVersion = guide.versionsguide.find(version => version.language === targetLanguage);

            if (targetVersion) {
                return {
                    guideId: targetVersion._id,
                    title: targetVersion.title,
                };
            } else {
                return {
                    guideId: null,
                    title: 'N/A',
                };
            }
        });

        return filtredGuides;
    }

    public async archiveGuideByLanguageAndId(language: Language, guideId: string): Promise<any> {
        const guideToDeleteFrom = await guideModel.findById(guideId);

        if (!guideToDeleteFrom) throw new HttpException(404, MESSAGES.GUIDE.NOT_FOUND);

        const versionIndex = guideToDeleteFrom.versionsguide.findIndex(version => version.language === language);

        if (versionIndex === -1) throw new HttpException(404, MESSAGES.GUIDE.MISSING_LANGUAGE_OR_VERSION_IDS);

        const versionToDelete = guideToDeleteFrom.versionsguide[versionIndex];

        versionToDelete.isArchived = !versionToDelete.isArchived;
        await guideToDeleteFrom.save();

        return guideToDeleteFrom;
    }
}
export default GuideService;
