"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const article_category_model_1 = __importDefault(require("./article.category.model"));
const article_interface_1 = require("../article.interface");
const mongoose_1 = __importDefault(require("mongoose"));
const article_model_1 = __importDefault(require("../article.model"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const messages_1 = require("@/utils/helpers/messages");
const mongoose_2 = require("mongoose");
const bson_1 = require("bson");
class CategoryArticleService {
    async getSlugBySlug(language, url) {
        const category = await article_category_model_1.default.findOne({ [`versions.${language}.url`]: url }).lean().exec();
        if (!category)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        const targetLanguage = language === 'en' ? 'fr' : 'en';
        const selectedVersion = category.versions[targetLanguage];
        if (!selectedVersion)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        return {
            slug: selectedVersion.url,
            name: selectedVersion.name,
        };
    }
    async createCategory(categoryData) {
        const duplicateChecks = Object.keys(categoryData.versions).map(language => ({
            [`versions.${language}.name`]: categoryData.versions[language].name.trim(),
        }));
        const duplicateCategory = await article_category_model_1.default.findOne({
            $or: duplicateChecks,
        });
        if (duplicateCategory)
            throw new http_exception_1.default(409, messages_1.MESSAGES.CATEGORY.ALREADY_EXIST);
        const existingUrls = new Set();
        const existingCategories = await article_category_model_1.default.find({});
        existingCategories.forEach(existingCategory => {
            // issue to resolve
            Object.values(existingCategory.versions).forEach((version) => {
                if (version.url) {
                    existingUrls.add(version.url);
                }
            });
        });
        const defaultLanguage = article_interface_1.Language.ENGLISH;
        const processedVersions = {};
        for (const [language, versionData] of Object.entries(categoryData.versions)) {
            const version = versionData;
            if (!version.language) {
                version.language = defaultLanguage;
            }
            let url = version.url || version.name.toLowerCase().replace(/\s+/g, '-');
            let count = 0;
            while (existingUrls.has(url)) {
                count++;
                url = `${url}-${count}`;
            }
            existingUrls.add(url);
            version.url = url;
            processedVersions[language] = version;
        }
        const newCategory = new article_category_model_1.default({
            ...categoryData,
            versions: processedVersions,
        });
        const savedCategory = await newCategory.save();
        for (const [language, version] of Object.entries(savedCategory.versions)) {
            const versionData = version;
            const newArticles = versionData?.articles || [];
            if (newArticles?.length > 0) {
                for (const articleId of newArticles) {
                    const articleVersion = await article_model_1.default.findOne({ 'versions._id': articleId });
                    if (articleVersion) {
                        const targetCategoryVersionIndex = articleVersion.versions.findIndex((ver) => ver._id.equals(articleId));
                        if (targetCategoryVersionIndex !== -1) {
                            const targetCategoryVersion = articleVersion.versions[targetCategoryVersionIndex];
                            if (!targetCategoryVersion.category.includes(versionData._id)) {
                                targetCategoryVersion.category.push(versionData._id);
                                await articleVersion.save();
                            }
                        }
                    }
                }
            }
        }
        return savedCategory;
    }
    async getAllCategories(queries) {
        const { paginated = 'true', pageNumber = 1, pageSize = 10, language, sortOrder = 'desc', name } = queries;
        const queryConditions = {};
        if (language) {
            queryConditions[`versions.${language}`] = { $exists: true };
        }
        if (name) {
            queryConditions[`versions.${language}.name`] = new RegExp(name, 'i');
        }
        let categoriesQuery = article_category_model_1.default.find(queryConditions);
        if (sortOrder) {
            categoriesQuery = categoriesQuery.sort({ createdAt: sortOrder === 'asc' ? 1 : -1 });
        }
        if (paginated === 'true') {
            const skip = (pageNumber - 1) * pageSize;
            categoriesQuery = categoriesQuery.skip(skip).limit(pageSize);
        }
        const categories = await categoriesQuery.lean();
        const filteredCategories = categories.map(category => {
            const versions = {};
            if (language) {
                if (category.versions[language]) {
                    versions[language] = category.versions[language];
                }
            }
            else {
                Object.assign(versions, category.versions);
            }
            return {
                _id: category._id,
                versions: versions,
                robotsMeta: category.robotsMeta,
            };
        });
        const totalCategories = await article_category_model_1.default.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalCategories / pageSize);
        return {
            pageNumber: Number(pageNumber),
            pageSize: Number(pageSize),
            totalPages,
            totalCategories,
            categoriesData: filteredCategories,
        };
    }
    async getAllCategoriesList(queries) {
        const { paginated = 'true', pageNumber = 1, pageSize = 10, language, sortOrder = 'desc', name } = queries;
        const queryConditions = {};
        if (language) {
            queryConditions[`versions.${language}`] = { $exists: true };
        }
        if (name) {
            queryConditions[`versions.${language}.name`] = new RegExp(name, 'i');
        }
        let categoriesQuery = article_category_model_1.default.find(queryConditions);
        if (sortOrder) {
            categoriesQuery = categoriesQuery.sort({ createdAt: sortOrder === 'asc' ? 1 : -1 });
        }
        if (paginated === 'true') {
            const skip = (pageNumber - 1) * pageSize;
            categoriesQuery = categoriesQuery.skip(skip).limit(pageSize);
        }
        const categories = await categoriesQuery.lean();
        const filteredCategories = categories.map(category => {
            const versions = {};
            if (language) {
                if (category.versions[language]) {
                    versions[language] = category.versions[language];
                }
            }
            else {
                Object.assign(versions, category.versions);
            }
            return {
                _id: category._id,
                versions: versions,
                robotsMeta: category.robotsMeta,
            };
        });
        const totalCategories = await article_category_model_1.default.countDocuments(queryConditions);
        return {
            categoriesData: filteredCategories,
            totalCategories,
        };
    }
    async getCategoriesByLanguage(language) {
        const categories = await article_category_model_1.default.find({ [`versions.${language}`]: { $exists: true } }).lean();
        const filteredCategories = categories
            .map(category => {
            const version = category.versions[language];
            return {
                _id: category._id,
                versions: {
                    [language]: {
                        name: version.name,
                        id: version._id,
                    },
                },
            };
        })
            .filter(category => category.versions[language]); // Filter out categories without the requested language
        return filteredCategories;
    }
    async addVersionToCategory(categoryId, newVersion) {
        const existingCategory = await article_category_model_1.default.findById(categoryId);
        if (!existingCategory)
            throw new http_exception_1.default(404, 'Category not found');
        const existingUrls = new Set();
        const existingCategories = await article_category_model_1.default.find({});
        existingCategories.forEach(category => {
            Object.values(category.versions).forEach((version) => {
                if (version.url) {
                    existingUrls.add(version.url);
                }
            });
        });
        if (!newVersion.url) {
            newVersion.url = `${newVersion.name.toLowerCase().replace(/\s+/g, '-')}`;
        }
        let tempUrl = newVersion.url;
        let count = 1;
        while (existingUrls.has(tempUrl)) {
            tempUrl = `${newVersion.url}-${count}`;
            count++;
        }
        newVersion.url = tempUrl;
        // Add the new version to the category
        existingCategory.versions[newVersion.language] = newVersion;
        const updatedCategory = await existingCategory.save();
        return updatedCategory;
    }
    async updateCategoryByLanguageAndId(language, categoryId, updateData) {
        const categoryToUpdate = await article_category_model_1.default.findById(categoryId);
        if (!categoryToUpdate)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        const existingUrls = new Set();
        const existingCategories = await article_category_model_1.default.find({});
        existingCategories.forEach(existingCategory => {
            Object.values(existingCategory.versions).forEach((version) => {
                if (version.url) {
                    existingUrls.add(version.url);
                }
            });
        });
        let updatedVersion = null;
        const version = categoryToUpdate.versions[language];
        if (version) {
            const previousVersion = JSON.parse(JSON.stringify(version));
            const titleChanged = updateData.name && updateData.name !== version.name;
            if (titleChanged) {
                const titleExists = await article_category_model_1.default.exists({
                    [`versions.${language}.name`]: updateData.name.trim(),
                    _id: { $ne: categoryToUpdate._id },
                });
                if (titleExists)
                    throw new http_exception_1.default(409, messages_1.MESSAGES.CATEGORY.TITLE_ALREADY_EXIST);
            }
            const urlChanged = updateData.url && updateData.url !== version.url;
            if (urlChanged) {
                const urlExists = await article_category_model_1.default.exists({ [`versions.${language}.url`]: updateData.url });
                if (urlExists)
                    throw new http_exception_1.default(409, messages_1.MESSAGES.CATEGORY.URL_ALREADY_EXIST);
                version.url = updateData.url || updateData.name.toLowerCase().replace(/\s+/g, '-');
            }
            Object.assign(version, updateData);
            updatedVersion = version;
            const defaultLanguage = article_interface_1.Language.ENGLISH;
            if (!version.language) {
                version.language = defaultLanguage;
            }
            // Update the version in the category
            categoryToUpdate.versions[language] = version;
            await categoryToUpdate.save();
            if (updatedVersion && updatedVersion.articles.length >= 0) {
                const previousArticles = previousVersion.articles.map((id) => id.toString());
                const newArticles = updateData.articles || [];
                const removedArticles = previousArticles.filter((articleId) => !newArticles.includes(articleId));
                const addedArticles = newArticles.filter((articleId) => !previousArticles.includes(articleId));
                for (const articleId of addedArticles) {
                    const articleVersion = await article_model_1.default.findOne({ 'versions._id': articleId });
                    if (articleVersion) {
                        const targetCategoryVersionIndex = articleVersion.versions.findIndex((ver) => ver._id.equals(articleId));
                        if (targetCategoryVersionIndex !== -1) {
                            const targetCategoryVersion = articleVersion.versions[targetCategoryVersionIndex];
                            if (!targetCategoryVersion.category.includes(updatedVersion._id)) {
                                targetCategoryVersion.category.push(updatedVersion._id);
                                await articleVersion.save();
                            }
                        }
                    }
                }
                for (const articleId of removedArticles) {
                    const articleVersion = await article_model_1.default.findOne({ 'versions._id': new mongoose_2.Types.ObjectId(articleId) });
                    if (articleVersion) {
                        const targetCategoryVersionIndex = articleVersion.versions.findIndex((ver) => ver._id.equals(articleId));
                        if (targetCategoryVersionIndex !== -1) {
                            const targetCategoryVersion = articleVersion.versions[targetCategoryVersionIndex];
                            if (updatedVersion && targetCategoryVersion.category.includes(updatedVersion._id)) {
                                targetCategoryVersion.category = targetCategoryVersion.category.filter((catId) => !catId.equals(updatedVersion?._id));
                                await articleVersion.save();
                            }
                        }
                    }
                }
            }
            return categoryToUpdate;
        }
        throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.VERSION_NOT_EXIST);
    }
    async upsertCategoryVersion(categoryId, language, versionData) {
        const existingCategory = await article_category_model_1.default.findById(categoryId);
        if (!existingCategory)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        const existingUrls = new Set();
        const existingCategories = await article_category_model_1.default.find({ _id: { $ne: categoryId } });
        existingCategories.forEach(category => {
            Object.values(category.versions).forEach((version) => {
                if (version.url) {
                    existingUrls.add(version.url);
                }
            });
        });
        // Check if version exists for this language
        const versionFound = existingCategory.versions[language] !== undefined;
        if (versionFound) {
            return await this.updateCategoryByLanguageAndId(language, categoryId, versionData);
        }
        else {
            let url = versionData.url || versionData.name.toLowerCase().replace(/\s+/g, '-');
            let count = 0;
            if (existingUrls.has(url)) {
                count = 1;
                while (existingUrls.has(`${url}-${count}`)) {
                    count++;
                }
                url = `${url}-${count}`;
            }
            existingUrls.add(url);
            const defaultLanguage = article_interface_1.Language.ENGLISH;
            if (!versionData.language) {
                versionData.language = defaultLanguage;
            }
            const newVersion = {
                language: versionData.language || language,
                name: versionData.name,
                url: url,
                description: versionData.description,
                articles: versionData.articles || [],
                metaTitle: versionData.metaTitle,
                metaDescription: versionData.metaDescription,
                image: versionData.image,
                createdAt: new Date(),
                updatedAt: new Date(),
                _id: new mongoose_1.default.Types.ObjectId(),
                canonical: versionData.canonical,
            };
            return await this.addVersionToCategory(categoryId, newVersion);
        }
    }
    async getCategoryByUrl(language, url, currentUser) {
        const categories = await article_category_model_1.default
            .aggregate([
            { $match: { [`versions.${language}.url`]: url.toLowerCase() } },
            {
                $addFields: {
                    currentVersion: { $objectToArray: '$versions' },
                },
            },
            { $unwind: '$currentVersion' },
            { $match: { 'currentVersion.k': language } },
            { $unwind: { path: '$currentVersion.v.articles', preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: 'articles',
                    let: { articleId: '$currentVersion.v.articles' },
                    pipeline: [
                        { $unwind: '$versions' },
                        { $match: { $expr: { $eq: ['$versions._id', '$$articleId'] } } },
                        {
                            $project: {
                                id: '$versions._id',
                                name: '$versions.name',
                                url: '$versions.url',
                            },
                        },
                    ],
                    as: 'articlesDetails',
                },
            },
            { $unwind: { path: '$articlesDetails', preserveNullAndEmptyArrays: true } },
            {
                $group: {
                    _id: {
                        id: '$_id',
                        idCategory: '$currentVersion.v._id',
                        language: '$currentVersion.v.language',
                        url: '$currentVersion.v.url',
                        name: '$currentVersion.v.name',
                        robotsMeta: '$robotsMeta',
                    },
                    articles: {
                        $push: {
                            id: '$articlesDetails.id',
                            name: '$articlesDetails.title',
                            url: '$articlesDetails.url',
                        },
                    },
                },
            },
            {
                $project: {
                    _id: '$_id.id',
                    versions: {
                        [language]: {
                            language: '$_id.language',
                            idCategory: '$_id.idCategory',
                            name: '$_id.name',
                            url: '$_id.url',
                            articles: {
                                $cond: {
                                    if: { $eq: ['$articles', [null]] },
                                    then: [],
                                    else: '$articles',
                                },
                            },
                        },
                    },
                    robotsMeta: '$_id.robotsMeta',
                },
            },
        ])
            .exec();
        if (categories?.length === 0)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        const category = categories[0];
        return {
            _id: category._id,
            versions: category.versions,
            robotsMeta: category.robotsMeta || '',
        };
    }
    async deleteCategoryByLanguageAndId(language, categoryId) {
        try {
            const categoryToDeleteFrom = await article_category_model_1.default.findById(categoryId);
            if (!categoryToDeleteFrom) {
                throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
            }
            const versionToDelete = categoryToDeleteFrom.versions[language];
            if (!versionToDelete) {
                throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.VERSION_NOT_EXIST);
            }
            // Remove category references from articles
            for (const articleId of versionToDelete.articles) {
                const articleVersion = await article_model_1.default.findOne({
                    'versions._id': articleId,
                });
                if (!articleVersion)
                    continue;
                const targetArticleVersion = articleVersion.versions.find(ver => ver._id.equals(articleId));
                if (!targetArticleVersion)
                    continue;
                const updatedCategories = targetArticleVersion.category.filter(catId => !catId.equals(versionToDelete._id));
                if (updatedCategories.length !== targetArticleVersion.category.length) {
                    targetArticleVersion.category = updatedCategories;
                    await articleVersion.save();
                }
            }
            // Remove the version from the category
            delete categoryToDeleteFrom.versions[language];
            await categoryToDeleteFrom.save();
            // If no versions left, delete the entire category
            if (Object.keys(categoryToDeleteFrom.versions).length === 0) {
                await article_category_model_1.default.deleteOne({ _id: categoryId });
            }
            return categoryToDeleteFrom;
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async getCategory(id) {
        try {
            const category = await article_category_model_1.default.findById(id).lean();
            if (!category)
                throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
            return category;
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async getOppositeLanguageVersionsCategory(language, versionIds) {
        const targetLanguage = language === 'en' ? 'fr' : 'en';
        // Find categories that have versions with the specified IDs
        const categories = await article_category_model_1.default
            .find({
            $or: [{ [`versions.${language}._id`]: { $in: versionIds } }, { [`versions.${targetLanguage}._id`]: { $in: versionIds } }],
        })
            .exec();
        if (!categories || categories.length === 0) {
            return [];
        }
        const filteredCategories = categories.map(category => {
            const targetVersion = category.versions[targetLanguage];
            if (targetVersion) {
                return {
                    _id: targetVersion._id,
                    name: targetVersion.name,
                };
            }
            else {
                return {
                    _id: null,
                    name: 'N/A',
                };
            }
        });
        return filteredCategories;
    }
    async convertToNewModel(file) {
        const legacyCategories = bson_1.BSON.EJSON.parse(file.buffer.toString());
        const convertedCategories = [];
        for (const legacy of legacyCategories) {
            const versions = {};
            if (Array.isArray(legacy.versionscategory)) {
                for (const version of legacy.versionscategory) {
                    const lang = version.language;
                    if (lang) {
                        if (version.articles && typeof version.articles === 'string') {
                            try {
                                const parsedArray = JSON.parse(version.articles.replace(/'/g, '"'));
                                if (Array.isArray(parsedArray)) {
                                    version.articles = parsedArray.map(item => item['$oid'] || item);
                                }
                            }
                            catch (e) {
                                console.error("Failed to parse 'articles' string:", e);
                                version.articles = [];
                            }
                        }
                        versions[lang] = {
                            ...version,
                            language: lang,
                        };
                    }
                }
            }
            delete legacy.versionscategory;
            console.log({
                ...legacy,
                versions,
            });
            const newCategory = new article_category_model_1.default({
                ...legacy,
                versions,
            });
            const saved = await newCategory.save();
            convertedCategories.push(saved);
        }
        return convertedCategories;
    }
}
exports.default = CategoryArticleService;
//# sourceMappingURL=article.category.service.js.map