"use client";
import { Container } from "@mui/material";
import CustomButton from "@/components/ui/CustomButton";
import SvgArrow from "@/assets/images/icons/arrow.svg";

import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import Svgnew from "@/assets/images/icons/new.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";
import contactMapImg from "@/assets/images/website/contactMapImg.png";
import { useTranslation } from "react-i18next";
import { websiteRoutesList } from "@/helpers/routesList";
import Link from "next/link";

function OfficesListForContactPage({ title, subtitle, children }) {
  const { t } = useTranslation();

  const contacts = [
    {
      title: t("contactUs:bureux:contacts:france"),
      locations: [
        "Atlantic Building Montparnasse, Entrance No. 7, 3rd floor, 75015 Paris",
      ],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.francePage.route}`,
    },
    {
      title: t("contactUs:bureux:contacts:switzerland"),
      locations: ["Grand-Rue 92, 1820 Montreux, Switzerland"],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.contact.route}`,
    },
    {
      title: t("contactUs:bureux:contacts:ksa"),
      locations: [t("ksa:officeLocation:address")],
      phones: ["+33 18 94 91 286", "+971 59 051 0291"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.ksaPage.route}`,
    },
    {
      title: t("contactUs:bureux:contacts:uae"),
      locations: [
        "HDS Business Center Office 306 JLT, Dubai, United Arab Emirate",
      ],
      phones: ["+971 (04) 4876 0672"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.dubaiPage.route}`,
    },
    {
      title: t("contactUs:bureux:contacts:qatar"),
      locations: [
        "Level 14, Commercial Bank Plaza, West Bay . Doha , Qatar, PO BOX : 27111",
      ],
      phones: ["+974 4452 7957"],
      email: "<EMAIL>",
      iconNew: <Svgnew />,
      link: `/${websiteRoutesList.qatarPage.route}`,
    },
    {
      title: t("contactUs:bureux:contacts:iraq"),
      locations: [
        "Baghdad, Al-Karrada District, Sector 905 J7, Building 51, Business Avenue, Office no. M.SH 6780",
      ],
      phones: ["+964 ************"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.iraqPage.route}`,
      iconNew: <Svgnew />,
    },
    {
      title: t("contactUs:bureux:contacts:tunisia"),
      locations: ["Imm. MADIBA، Rue Khawarizmi, La Goulette 2015 Tunisia"],
      phones: ["+216 31 385 510"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.tunisiaPage.route}`,
    },
    {
      title: t("contactUs:bureux:contacts:hydra"),
      locations: [
        "Route les oliviers les cretes n°14, 16035 Hydra, Alger, Algeria",
      ],
      phones: ["+213 982 30 13 29"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.algeriaPage.route}`,
    },
    {
      title: t("contactUs:bureux:contacts:hassiMassoud"),
      locations: [
        "Eurojapan Residence Route Nationale N°3 BP 842, Hassi Messaoud, Algeria",
      ],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.algeriaPage.route}`,
    },
    {
      title: t("contactUs:bureux:contacts:morocco"),
      locations: [
        "Zenith 1, Sidi maarouf, lot CIVIM, Casablanca 20270, Morocco",
      ],
      phones: ["+212 5 22 78 63 66"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.moroccoPage.route}`,
    },
    {
      title: t("contactUs:bureux:contacts:egypte"),
      locations: ["8 El Birgas street, Garden City, Egypt"],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.egyptePage.route}`,
    },
    {
      title: t("contactUs:bureux:contacts:lybia"),
      locations: [" Al Serraj, AlMawashi Street P.O.Box 3000, Tripoli, Libya"],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.libyaPage.route}`,
    },
  ];

  return (
    <div id="offices-list-contact-page">
      <Container className="top-section custom-max-width">
        <h1 className="heading-h1 text-white text-center">
          {title || t("contactUs:OfficesListForContactPage:title")}
        </h1>
        <p className="sub-heading text-white text-center">
          {subtitle || t("contactUs:OfficesListForContactPage:description")}
        </p>
        <div className="link-offices-page">
          <CustomButton
            text={t("contactUs:bureux:europeanOffices")}
            className={"btn yellow"}
            link={`/${websiteRoutesList.europePage.route}`}
          />
          <CustomButton
            text={t("contactUs:bureux:middleEastOffices")}
            className={"btn yellow"}
            link={`/${websiteRoutesList.middleEastPage.route}`}
          />

          <CustomButton
            text={t("contactUs:bureux:aficaOffices")}
            className={"btn yellow"}
            link={`/${websiteRoutesList.africaPage.route}`}
          />
          <Link
            href={`/${websiteRoutesList.contact.route}/#contact-page-form`}
            className={"btn yellow"}
          >
            {t("contactUs:bureux:restOfTheWold")}
          </Link>
        </div>
        <Link href={`#our-location-section`} style={{ textDecoration: "none" }}>
          <CustomButton
            text={t("contactUs:bureux:ourOffices")}
            className={"btn btn-filled offices"}
            icon={<SvgArrow />}
          />
        </Link>
      </Container>
      <div className="center-img">
        <img
          alt={t("contactUs:bureux:altImg")}
          src={contactMapImg.src}
          loading="lazy"
          width={300}
          height={300}
        />
      </div>
      <Container
        className="contact-items-section custom-max-width"
        id="our-location-section"
      >
        {contacts.map((contact, index) => (
          <div className="contact-item" key={index}>
            <div>
              <div style={{ display: "flex", justifyContent: "space-between" }}>
                <p className="title text-yellow">{contact.title}</p>
                {contact.iconNew && <span>{contact.iconNew}</span>}
              </div>
              <div>
                {contact.locations.map((location, locIndex) => (
                  <p className="row-item" key={locIndex}>
                    <span>
                      <SvglocationPin />
                    </span>
                    {location}
                  </p>
                ))}

                <p className="row-item">
                  <span>
                    <SvgcallUs />
                  </span>
                  {contact.phones.map((phone, phoneIndex) => (
                    <>
                      {" "}
                      {contact.phones.length > 1 ? (
                        <>
                          {phone} <br />
                        </>
                      ) : (
                        phone
                      )}
                    </>
                  ))}
                </p>

                <p className="row-item">
                  <span>
                    <Svgemail />
                  </span>
                  {contact.email}
                </p>
              </div>
            </div>
            <CustomButton
              text={t("contactUs:bureux:contacts:viewDetails")}
              className={"btn btn-outlined white"}
              onClick={() => (window.location.href = contact.link)}
            />
          </div>
        ))}
      </Container>
      {children}
    </div>
  );
}

export default OfficesListForContactPage;
