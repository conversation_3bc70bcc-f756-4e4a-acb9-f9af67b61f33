"use client";

import { useTranslation } from "react-i18next";
import AuthLayout from "@/components/layouts/AuthLayout";
import ConfirmApplication from "../../../../features/application/component/activation/ConfirmApplication.jsx";

const page = () => {
  const { t } = useTranslation();
  return (
    <AuthLayout id="auth-layout">
      <ConfirmApplication t={t} />
    </AuthLayout>
  );
};

export default page;
