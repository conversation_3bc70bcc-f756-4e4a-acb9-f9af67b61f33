"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("@/utils/helpers/constants");
const categoryVersionSchema = new mongoose_1.Schema({
    language: { type: String, enum: constants_1.Language, required: true },
    name: { type: String },
    url: { type: String, unique: true },
    description: { type: String },
    image: { type: String, default: null },
    canonical: { type: String },
    articles: [{ type: mongoose_1.Types.ObjectId, ref: 'Article' }],
    guides: [{ type: mongoose_1.Types.ObjectId, ref: 'Guide' }],
    metaTitle: { type: String, default: '' },
    metaDescription: { type: String, default: '' },
    createdAt: { type: Date, required: true, default: Date.now },
    updatedAt: { type: Date, required: true, default: Date.now },
});
const categorySchema = new mongoose_1.Schema({
    versions: {
        type: Map,
        of: categoryVersionSchema,
        required: true,
    },
    robotsMeta: {
        type: String,
        enum: constants_1.robotsMeta,
        default: constants_1.robotsMeta.index,
    },
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.__v;
        },
    },
});
exports.default = (0, mongoose_1.model)('Category', categorySchema);
//# sourceMappingURL=article.category.model.js.map