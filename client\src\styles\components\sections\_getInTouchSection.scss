#get-in-touch {
  padding-bottom: 50px;
  padding-top: 50px;

  .text-center {
    width: fit-content;
    text-align: center;
    justify-self: center;
  }

  img {
    margin: 20px 0;
  }
}

.service-image {
  margin: auto;
  border: 1px solid $gradientBlue;
  padding: 20px;
  height: 332px;
  width: 332px;

  @include media-query(mobile) {
    height: 250px;
    width: 250px;
  }

  @include media-query(tablet) {
    max-width: 300px;
  }

  > div {
    width: 100%;
    height: 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }
}
.ksa {
  max-width: 100% !important;
}

#global-hr-services-website {
  padding: 50px 0px 100px;
  background: linear-gradient(71deg, #0b3051 0%, #234791 100%);
  max-width: 1240px;
  place-self: center;
  display: flex;
  overflow-x: hidden;
  position: relative;
  flex-wrap: wrap;
  place-content: center;

  @include media-query(tablet) {
    max-width: 80%;
  }

  @include media-query(mobile) {
    max-width: 80%;
    padding: 50px 0px;
    margin: 40px;
  }

  .services-list {
    &.ksa {
      margin: 0px !important;
    }
    margin: 20px;
    flex-wrap: wrap;
    justify-content: space-evenly;
    display: flex;
    white-space: nowrap;

    @include media-query(mobile, tablet) {
      flex-wrap: nowrap;
      overflow-x: auto;
      gap: 16px;
      padding: 8px;
    }
  }

  .service-item {
    color: white;
    cursor: pointer;
    margin: 0px 10px;
    display: flex;
    align-items: center;
    padding: 5px 20px;
    background: transparent;
    border: none;
    font-size: 16px;

    svg {
      margin-right: 5px;
    }

    path {
      fill: white;
    }

    @include media-query(mobile, tablet) {
      padding: 5px 10px;
      margin: 0;
    }

    // .service-title {
    //   // padding: 10px 20px;
    // }
  }

  .service-item-yellow {
    color: $blue;
    background: $yellow;
    border-radius: 5px;
    margin: 0px 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 5px 20px;
    border: none;
    font-size: 16px;

    svg {
      margin-right: 5px;
    }

    path {
      fill: $blue;
    }

    .service-title {
      font-weight: 600;
    }

    @include media-query(mobile, tablet) {
      padding: 5px 10px;
      margin: 0;
    }
  }

  .service {
    display: flex;
    max-width: 820px;
    margin: 40px 0px 0px;
    padding: 30px;
    background: #143d6f;
    box-shadow: 0px 0px 5px -2px;
    z-index: 1;

    @include media-query(mobile, tablet) {
      display: block;
    }

    @include media-query(mobile) {
      max-width: 280px;
      margin: 20px;
    }
  }

  .svg-service-icon {
    position: absolute;
    left: 30px;
    top: 300px;
    &.ksa {
      left: 13% !important;
      top: 200px;
      @include media-query(extraLargeScreens) {
        left: 20% !important;
      }
    }
  }
  .list-arrow {
    list-style: none;
    padding-inline-start: 0px;
  }
  .item-arrow {
    font-size: 17px;
    margin: 5px 0;
  }
  .item-arrow::before {
    content: "➤"; /* Use an arrow character */
    color: #ffffff; /* Optional: Change arrow color */
    margin-right: 8px; /* Add some space between arrow and text */
  }
  .service-subtitle {
    color: #ffffff;
    font-size: 17px;
    font-family: "Proxima-Nova-SemiBold" !important;
  }

  .service-content-title {
    font-size: 25px;
    color: $yellow;
    font-weight: 600;
    margin-bottom: 10px;
  }

  .service-content {
    color: white;
    font-size: 20px;
    max-width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    a {
      font-size: 20px;
    }

    @include media-query(laptops) {
      // max-width: 440px;
    }
    // max-width: 470px;

    @include media-query(mobile) {
      max-width: 100%;
      .btn {
        padding: 20px 0;
      }
    }
  }

  .service-content-image {
    margin: auto;
    padding-left: 20px;
    height: 332px;
    width: 332px;

    @include media-query(mobile) {
      height: 250px;
      width: 250px;
      padding-left: 0px;
      margin-top: 10px;
    }

    @include media-query(tablet) {
      max-width: 350px;
    }

    > div {
      width: 100%;
      height: 100%;
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
    }
  }
}

#global-hr-services {
  padding: 50px 0;
  @include gradientBg();

  .custom-max-width {
    max-width: 1300px !important;
    margin-left: auto;
    margin-right: auto;
  }

  .css-fgq1ej-MuiPaper-root-MuiAccordion-root::before {
    height: 0;
    display: none;
  }

  .services-accordion {
    background-color: transparent;

    &-header {
      background-color: transparent;
    }

    .service-title {
      color: $white;
      font-size: 24px;
      font-family: "Proxima-Nova-Semibold" !important;

      &:before {
        content: "";
        width: 12px;
        height: 12px;
        display: inline-flex;
        background: $white;
        margin-right: 10px;
      }
    }

    .Mui-expanded {
      &:before {
        display: "none";
      }

      .service-title:before {
        background: $yellow;
      }
    }

    .service-description {
      color: $white;
    }
  }
}

#what-they-say {
  padding: 50px 0;
  @include gradientBg();

  .css-fgq1ej-MuiPaper-root-MuiAccordion-root::before {
    height: 0;
    display: none;
  }

  .say-section {
    display: flex;
    overflow-x: hidden;
    width: 100%;
    position: relative;
    overflow-y: hidden;
    margin-top: 20px;

    @include media-query(tablet, mobile) {
      overflow-x: scroll;
      margin: auto 0px auto 30px;
    }
  }

  .say-item {
    background: #234574;
    border-radius: 5px;
    height: auto;
    max-width: 27%;
    margin: 10px 20px;
    padding: 25px;
    flex: 0 0 auto;
    transition: transform 0.3s ease-in-out;

    @include media-query(mobile) {
      min-width: 70%;
      margin: 50px 20px;
    }

    @include media-query(tablet) {
      min-width: 50%;
      margin: 50px 20px;
    }

    @include media-query(laptops) {
      max-width: 24%;
    }
  }

  .say-item.scale-up {
    transform: scale(1.1);
    z-index: 2;
  }

  .say-item.scale-down {
    transform: scale(0.85);
    z-index: 2;
  }

  .say-content {
    color: white;
    line-height: 25px;
  }

  .guillemet {
    margin-bottom: 20px;
  }

  .buttons {
    margin: 40px auto 0px;
  }

  .scroll-button {
    border: none;
    cursor: pointer;
    background: transparent;
  }

  .scroll-button.left {
    margin-left: 5px;
  }

  .scroll-button.right {
    margin-left: 5px;
  }

  .star-fill {
    display: flex;
    margin: 20px 0px;
    scale: 0.7;
    -webkit-transform-origin-x: left;
  }

  .author {
    display: flex;
    align-items: center;

    .css-pfutxk-MuiAvatar-root {
      position: relative;
      display: flex;
      -webkit-box-align: center;
      align-items: center;
      -webkit-box-pack: center;
      justify-content: center;
      flex-shrink: 0;
      font-size: 1.25rem;
      line-height: 1;
      border-radius: 50%;
      overflow: hidden;
      user-select: none;
      color: #fff;
      background-color: #bdbdbd;
      width: 50px;
      height: 50px;
    }
  }

  .author-icon {
    color: $blue;
    text-transform: uppercase;
    font-size: 30px;
    font-weight: 600;
  }

  .info-author {
    margin-left: 12px;
    color: white;
  }

  .name {
    font-size: 16px;
  }

  .quality {
    font-size: 14px;
    font-weight: 600;
  }
}

#global-map {
  padding-top: 50px;
  padding-bottom: 50px;
  background-color: transparent;

  .sub-heading {
    color: $white;
  }

  .container {
    display: flex;
    justify-content: center;
  }

  .buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;

    button {
      padding: 10px 20px;
      cursor: pointer;
    }
  }

  .infoSection {
    display: flex;
    justify-content: center;
    width: 100%;

    .infoBox {
      padding: 20px;
      border: 1px solid #ccc;
      background-color: #f9f9f9;
      width: 300px;
      text-align: center;
    }
  }

  #btns-zone {
    display: flex;
    flex-direction: row;
    padding-top: 10px;
    padding-bottom: 25px;
    flex-wrap: wrap;
    justify-content: center;

    .btn-zone {
      width: auto;
      white-space: nowrap;
      margin: 0 10px;

      @include media-query(mobile) {
        width: 100%;
        margin: 10px 0;
      }
    }
  }

  #btns-country {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 10px;
    padding-bottom: 15px;

    @include media-query(mobile) {
      grid-template-columns: repeat(3, 1fr);
    }

    overflow-x: auto;
    white-space: nowrap;

    /* Scrollbar Styling */
    &::-webkit-scrollbar {
      height: 5px;
    }

    &::-webkit-scrollbar-track {
      background-color: $lightBlue2;
      -webkit-border-radius: 10px;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
      -webkit-border-radius: 10px;
      border-radius: 10px;
      background: $blue;
    }

    .btn-country {
      width: 100%;
      white-space: nowrap;
      @include media-query(mobile) {
        padding: 10px !important;
        width: -webkit-fill-available;
      }
    }
  }
}

.gradient-blue {
  background-image: linear-gradient(to bottom, $bankingColor, $blue);
}

#map-frame,
#map-frame-mobile {
  background-color: transparent;
  height: auto;
  width: -webkit-fill-available;
  margin: auto;

  .box-info {
    opacity: 0;
    z-index: -1;
    display: none;
  }

  .box-info.selected {
    display: block;
    opacity: 1;
    position: relative;
    z-index: 9999;
  }

  path.pin.selected {
    fill: $yellow;
    @include industriesFillSvgColors();
    // &.blue{
    //   fill: red;
    // }
  }
}

.numbers {
  display: flex;
  justify-content: center;
  padding: 0;
  margin-bottom: 50px;
  @include media-query(mobile) {
    margin-bottom: 20px;
  }
  .img-nbr {
    margin-bottom: 50px;
    width: 100%;
    height: auto;
  }

  svg {
    display: flex;
    margin: auto;
  }
}

.responsive-row-title-text {
  padding-top: 50px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  @include media-query(mobile) {
    flex-direction: column;
  }
  .grid-item {
    width: 48%;

    @include media-query(mobile) {
      width: 100%;
    }
  }
  @include media-query(mobile) {
    padding-bottom: 50px;
    padding-top: 10px;
  }

  .section {
    // max-width: 500px;

    @include media-query(mobile) {
      max-width: 330px;
    }
  }
}

.dark-bg {
  @include gradientBg();

  .content {
    background-color: transparent !important;
  }
}

.service-row {
  padding-top: 50px;
  padding-bottom: 50px;

  .label.yellow {
    color: $yellow;
    font-size: 16px;
    font-family: "Proxima-Nova-Medium" !important;
  }

  .reverse {
    flex-direction: row-reverse;

    .feauture-img {
      margin-left: 0;
      margin-right: 20px;

      @include media-query(mobile, tablet) {
        margin: 10px auto;
      }
    }
  }

  .MuiGrid-item {
    display: flex;
  }

  .content,
  .feauture-img {
    display: flex;
    justify-content: center;
    padding: 50px;
    flex-direction: column;
    // height: 100%;
    width: 100%;

    @include media-query(mobile, tablet) {
      padding: 20px;
    }
  }

  .content {
    background-color: $bankingColor;
  }

  .feauture-img {
    margin-left: 20px;
    border: 2px solid $gradientBlue;

    @include media-query(mobile, tablet) {
      max-height: 300px;
      width: 100%;
      text-align: center;
      margin: 10px auto;
    }

    img {
      height: 100%;
      width: 100%;

      @include media-query(mobile, tablet) {
        max-height: 300px;
        width: auto;
        margin: auto;
        text-align: center;
      }
    }
  }

  .data {
    margin: 30px 0;

    .data-item {
      font-size: 18px;
      color: $blue;
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      margin: 10px 0;

      &.text-white {
        color: $white;
      }

      svg {
        width: 50px;
        margin-right: 5px;
      }
    }
  }

  .data-item-paragraph {
    .sub-heading {
      display: flex;
      align-items: center;
      font-family: "Proxima-Nova-Medium" !important;
    }

    svg {
      width: 50px;
      margin-right: 5px;
    }
  }

  .btn {
    width: fit-content;
  }
}

#services-page {
  #expert-sourcing {
    margin-bottom: 50px;
    margin-top: 50px;
  }
}

#our-industries {
  @extend #banner-component;
  // margin-bottom: 50px;
  padding: 30px 0;

  .heading-h1 {
    margin-bottom: 20px !important;
    margin-top: 0 !important;
  }

  #industries__slider {
    overflow: hidden;

    .embla__container {
      display: flex;
    }

    .embla__slide {
      // flex: 0 0 19%;
      flex: 0 0 25%;
      min-width: 0;
      height: 280px;
      margin: 6px auto;
      padding: 0;

      @include media-query(tablet) {
        margin: 5px 5px 5px 0;
        flex: 0 0 31%;
      }

      @include media-query(mobile) {
        margin: 5px 5px 5px 0;
        flex: 0 0 64%;
      }

      .btn-ghost {
        padding: 15px 0;
      }

      .smart-square {
        width: 216px;
        height: 268px;
        padding: 10px 16px !important;
      }

      .slide__container {
        @include industriesBgColors();

        .embla__slide__content {
          display: flex;
          flex-direction: column;
          height: 100%;
          justify-content: flex-start;

          .embla__slide__description {
            color: $white;
            font-size: 14px;
            margin-top: 10px;
          }

          .embla__slide__industry-icon {
            justify-content: center;
            display: flex;
            height: fit-content;
            width: fit-content;
            margin: 20px auto 20px auto;
            transform: scale(2, 2);
          }

          .embla__slide__industry-name,
          .embla__slide__industry-icon {
            text-align: center;
            color: $white;
            font-size: 16px;
            text-transform: capitalize !important;
          }
        }
      }
    }
  }
}

.stats-container-pentabell {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0;

  @include media-query(mobile, tablet) {
    grid-template-columns: repeat(3, 1fr);
    margin: auto;
    scale: 0.75;
  }

  .stat-box {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 2px solid $blue;
    aspect-ratio: 1 / 1;
    width: 100%;
    height: 100%;
    box-sizing: border-box;

    @include media-query(mobile) {
      padding: 10px;
    }
  }

  .stat-box h2 {
    margin: 0;
    font-size: 3rem;
    color: $blue;
    font-weight: bold;
    @include media-query(mobile) {
      font-size: 2rem;
    }
  }

  .stat-box p {
    margin: 5px 0 0;
    font-size: 1.4rem;
    color: $blue;
  }

  .corner {
    position: absolute;
    width: 16px;
    height: 16px;
    background-color: currentColor;
  }

  .purple {
    z-index: 8;
    color: $telecomColor;
    border-color: $telecomColor;
    @include media-query(mobile, tablet) {
      margin-left: 1px;
      height: 150px;
      width: 150px;
    }

    .bottom-right {
      display: block;
      @include media-query(mobile, tablet) {
        display: none;
      }
    }
  }

  .blue,
  .red,
  .green {
    margin-top: -2px;
    @include media-query(mobile, tablet) {
      margin-top: 0;
      height: 150px;
      width: 150px;
    }
  }

  .red {
    margin-left: -3px;
    z-index: 7;
    color: $energyColor;
    border-color: $energyColor;
    .bottom-left {
      display: block;
      @include media-query(mobile, tablet) {
        display: none;
      }
    }
    @include media-query(mobile, tablet) {
      margin-left: -3px;
      height: 150px;
      width: 150px;
    }
  }

  .yellow {
    margin-left: -6px;
    color: $oilGazColor;
    border-color: $oilGazColor;
    @include media-query(mobile, tablet) {
      z-index: 9;
      margin-left: -1px;
      margin-top: -2px;
      height: 150px;
      width: 150px;
    }
    .bottom-left,
    .bottom-right {
      display: block;
      @include media-query(mobile, tablet) {
        display: none;
      }
    }
  }

  .green {
    margin-left: -8px;
    z-index: 9;
    color: $transportColor;
    border-color: $transportColor;
    @include media-query(mobile, tablet) {
      margin-left: 1px;
      margin-top: -4px;
    }

    .top-right,
    .bottom-right {
      display: none;
      @include media-query(mobile, tablet) {
        display: block;
      }
    }
  }

  .blue {
    margin-left: -10px;
    z-index: 10;
    color: $bankingColor;
    border-color: $bankingColor;
    @include media-query(mobile, tablet) {
      margin-left: -3px;
      margin-top: -4px;
    }
  }
  // corner style
  .top-left {
    top: -9px;
    left: -9px;
  }

  .top-right {
    top: -8px;
    right: -8px;
  }

  .bottom-left {
    bottom: -8px;
    left: -8px;
  }

  .bottom-right {
    bottom: -8px;
    right: -8px;
  }

  /* Assign specific grid positions */
  .stat-box:nth-child(1) {
    grid-column: 1;
    /* First column */
    grid-row: 1;
    /* First row */
  }

  .stat-box:nth-child(2) {
    grid-column: 2;
    /* Second column */
    grid-row: 2;
    /* Second row */

    @include media-query(mobile, tablet) {
      grid-column: 3;
      grid-row: 1;
    }
  }

  .stat-box:nth-child(3) {
    grid-column: 3;
    /* Second column */
    grid-row: 1;
    /* First row */

    @include media-query(mobile, tablet) {
      grid-column: 2;
      grid-row: 2;
    }
  }

  .stat-box:nth-child(4) {
    grid-column: 4;
    /* First column */
    grid-row: 2;
    /* Second row */

    @include media-query(mobile, tablet) {
      grid-column: 1;
      grid-row: 3;
    }
  }

  .stat-box:nth-child(5) {
    grid-column: 5;
    /* First column */
    grid-row: 2;

    /* Third row */
    @include media-query(mobile, tablet) {
      grid-column: 3;
      grid-row: 3;
    }
  }
}
