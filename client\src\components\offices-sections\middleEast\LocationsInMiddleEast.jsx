import Qatar from "@/assets/images/offices/Qatar.webp";
import UAE from "@/assets/images/offices/UAE.webp";
import KSA from "@/assets/images/offices/KSA.webp";
import Iraq from "@/assets/images/offices/Iraq.webp";
import Svgnew from "@/assets/images/icons/new.svg"
import { OfficesCountries } from "@/config/countries";
import { websiteRoutesList } from "@/helpers/routesList";
import LocationsList from "@/components/ui/LocationsList";

function LocationsInMiddleEast({ t }) {
  const contacts = [
    {
      title: t("middleeast:locations:Qatar:title"),
      locations: [
        "Address : Level 14, Commercial Bank Plaza, West Bay . Doha , Qatar, PO BOX : 27111",
      ],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.qatarPage.route}`,
      img: Qatar,
      country: OfficesCountries.Qatar,
      alt: t("middleeast:locations:Qatar:alt"),
    },
    {
      title: t("middleeast:locations:UAE:title"),
      locations: [
        "Adress : HDS Business Center Office 306 JLT, Dubai, United Arab Emirates",
      ],
      phones: ["+216 31 385 510"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.dubaiPage.route}`,
      img: UAE,
      country: OfficesCountries.UAE,
      alt: t("middleeast:locations:UAE:alt"),
    },
    {
      title: t("middleeast:locations:KSA:title"),
      locations: [
        t("ksa:officeLocation:address"),
      ],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.ksaPage.route}`,
      img: KSA,
      country: OfficesCountries.SaudiArabia,
      alt: t("middleeast:locations:KSA:alt"),
    },
    {
      title: t("middleeast:locations:iraq:title"),
      locations: [
        "Address : Baghdad, Al-Karrada District, Sector 905 J7, Building 51, Business Avenue, Office no. M.SH 6780",
      ],
      phones: ["+964 ************"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.iraqPage.route}`,
      img: Iraq,
      country: OfficesCountries.IRAQ,
      alt: t("middleeast:locations:iraq:alt"),
      iconNew: <Svgnew />,
    },

  ];

  return (
    <LocationsList
      t={t}
      contacts={contacts}
      sectionSubtitleKey="middleeast:locations:subTitle"
      sectionTitleKey="middleeast:locations:title"
    />
  );
}
export default LocationsInMiddleEast;