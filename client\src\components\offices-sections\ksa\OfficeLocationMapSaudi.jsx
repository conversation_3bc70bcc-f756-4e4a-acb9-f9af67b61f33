"use client";
import { Con<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, useMediaQuery, useTheme } from "@mui/material";
import CustomButton from "../@/components/ui/CustomButton";
import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";
import { websiteRoutesList } from "@/helpers/routesList";
import { useTranslation } from "react-i18next";

function OfficeLocationMapSaudi() {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.down("md"));
  return (
    <>
      {isMobile || isTablet ? (
        <Container id="office-location-map" className="custom-max-width">
          <Grid
            className="container"
            justifyContent="space-between"
            container
            spacing={0}
          >
            <Grid item xs={12} sm={6}>
              <div className="content">
                <p className="heading-h2 text-white">
                  The Staffing & HR Solutions Specialists in Saudi Arabia
                </p>
                <p className="sub-heading text-white">
                  International Payroll & Talent Acquisition Agency | Pentabell
                  KSA
                </p>
                <div>
                  <p className="paragraph text-white" key={"tn"}>
                    <span>
                      <SvglocationPin />
                    </span>
                   {t("ksa:officeLocation:address")}
                  </p>
                  <p className="paragraph text-white">
                    <span>
                      <SvgcallUs />
                    </span>
                    +33 18 94 91 286
                  </p>
                  <p className="paragraph text-white">
                    <span>
                      <SvgcallUs />
                    </span>
                    +971 59 051 0291
                  </p>
                  <p className="paragraph text-white">
                    <span>
                      <Svgemail />
                    </span>
                    <EMAIL>
                  </p>
                </div>
                <Link
                  href={`#service-page-form`}
                  className={"btn btn-outlined white"}
                >
                  {" "}
                  {t("ksa:officeLocation:talk")}
                </Link>
              </div>
            </Grid>
            <Grid item xs={12} sm={6}>
              <div className="map-frame">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d116010.55835082967!2d46.738122!3d24.681182!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e2f05b3448b2c0d%3A0x9f0e3f7f9fc831f!2sInternational%20recruitment%20%26%20staffing%20agency%20Saudi%20arabia%20-%20Pentabell%20KSA!5e0!3m2!1sfr!2sus!4v1728636716136!5m2!1sfr!2sus"
                  allowfullscreen=""
                  priority
                  referrerpolicy="no-referrer-when-downgrade"
                ></iframe>
              </div>
            </Grid>
          </Grid>
        </Container>
      ) : (
        <Container id="office-location-map-ksa" className="custom-max-width">
          <Grid
            className="container"
            justifyContent="space-between"
            container
            spacing={0}
            sx={{ position: "relative", alignItems: "center" }}
          >
            <Grid item xs={12} sm={12}>
              <div className="map-frame">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d116056.09761800473!2d46.73674870898437!3d24.63219487963867!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e2f05b3448b2c0d%3A0x9f0e3f7f9fc831f!2sInternational%20recruitment%20%26%20staffing%20agency%20Saudi%20arabia%20-%20Pentabell%20KSA!5e0!3m2!1sfr!2sus!4v1740581555467!5m2!1sfr!2sus"
                  allowfullscreen=""
                  priority
                  referrerpolicy="no-referrer-when-downgrade"
                ></iframe>
              </div>
            </Grid>
            {/* <Grid item xs={12} sm={10} className="content"> */}
            <div className="content">
              <p className="heading-h2 text-white text-center">
                {t("ksa:location:title")}
              </p>
              <p className="sub-heading text-white">
                {t("ksa:location:subtitle")}
              </p>
              <div className="infos">
                <p className="paragraph text-white" key={"tn"}>
                  <span>
                    <SvglocationPin />
                  </span>
                  {t("ksa:officeLocation:address")}
                </p>
                <div className="phone-wrapper">
                  <p className="paragraph text-white">
                    <span>
                      <SvgcallUs />
                    </span>
                    +33 18 94 91 286 | +971 59 051 0291
                  </p>
                </div>
                <p className="paragraph text-white">
                  <span>
                    <Svgemail />
                  </span>
                  <EMAIL>
                </p>
              </div>
              <Link
                href={`#service-page-form`}
                className={"btn btn-outlined white"}
              >
                {" "}
                {t("ksa:officeLocation:talk")}
              </Link>
            </div>
          </Grid>
          {/* </Grid> */}
        </Container>
      )}
    </>
  );
}

export default OfficeLocationMapSaudi;
