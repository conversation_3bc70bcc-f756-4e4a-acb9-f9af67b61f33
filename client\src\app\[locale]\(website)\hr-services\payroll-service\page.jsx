import BannerComponents from "@/components/pages/sites/sections/BannerComponents";
import banner from "@/assets/images/website/banner/Payroll-Pentabell-Services.webp";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import ServiceRow from "@/components/ui/ServiceRow";

import payrollServiceImgS1 from "../../../../../assets/images/services/payrollServiceImgS1.webp";
import payrollServiceImgS2 from "../../../../../assets/images/services/payrollServiceImgS2.webp";
import payrollServiceImgS3 from "../../../../../assets/images/services/payrollServiceImgS3.webp";
import ServicePageForm from "@/features/forms/components/ServicePageForm";
import ApproachToPayroll from "@/components/pages/sites/services/ApproachToPayroll";
import HowItWorks from "@/components/pages/sites/services/HowItWorks";
import WhyPentabell from "@/components/pages/sites/services/WhyPentabell";
import AskQuestions from "@/components/pages/sites/services/AskQuestions";
import initTranslations from "@/app/i18n";
import { axiosGetJsonSSR } from "@/config/axios";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${locale !== "en" ? `${locale}/` : ""
    }hr-services/payroll-service/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/hr-services/payroll-service/`,
    en: `https://www.pentabell.com/hr-services/payroll-service/`,
    "x-default": `https://www.pentabell.com/hr-services/payroll-service/`,
  };

  const { t } = await initTranslations(locale, ["payrollService", "global"]);
  try {
    const encodedSlug = encodeURIComponent("hr-services/payroll-service");
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/${encodedSlug}`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("payrollService:metaTitle"),
    description: t("payrollService:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}
async function payrollService({ params: { locale } }) {
  const { t } = await initTranslations(locale, ["payrollService", "global"]);

  const dataS1 = {
    title: t("payrollService:dataS1:title"),
    label: t("payrollService:dataS1:label"),
    paragraph: t("payrollService:dataS1:paragraph"),
    altImg: t("payrollService:dataS1:altImg"),
    featureImg: payrollServiceImgS1,
  };
  const dataS2 = {
    title: t("payrollService:dataS2:title"),
    label: t("payrollService:dataS2:label"),
    paragraph: t("payrollService:dataS2:paragraph"),
    altImg: t("payrollService:dataS2:altImg"),
    featureImg: payrollServiceImgS2,
  };
  const dataS3 = {
    title: t("payrollService:dataS3:title"),
    label: t("payrollService:dataS3:label"),
    paragraph: t("payrollService:dataS3:paragraph"),
    altImg: t("payrollService:dataS3:altImg"),
    featureImg: payrollServiceImgS3,
  };

  const ASK_QUESTIONS = [
    {
      id: "s1",
      title: t("payrollService:questions:QA1:question"),
      description: t("payrollService:questions:QA1:answer"),
    },
    {
      id: "s2",
      title: t("payrollService:questions:QA2:question"),
      description: t("payrollService:questions:QA2:answer"),
    },
    {
      id: "s3",
      title: t("payrollService:questions:QA3:question"),
      description: t("payrollService:questions:QA3:answer"),
    },
    {
      id: "s4",
      title: t("payrollService:questions:QA4:question"),
      description: t("payrollService:questions:QA4:answer"),
    },

    {
      id: "s6",
      title: t("payrollService:questions:QA6:question"),
      description: t("payrollService:questions:QA6:answer"),
    },
  ];

  const steps = [
    {
      title: t("payrollService:howItWorks:steps:step1:title"),
      description: t("payrollService:howItWorks:steps:step1:description"),
    },
    {
      title: t("payrollService:howItWorks:steps:step2:title"),
      description: t("payrollService:howItWorks:steps:step2:description"),
    },
    {
      title: t("payrollService:howItWorks:steps:step3:title"),
      description: t("payrollService:howItWorks:steps:step3:description"),
    },
    {
      title: t("payrollService:howItWorks:steps:step4:title"),
      description: t("payrollService:howItWorks:steps:step4:description"),
    },
  ];

  const reasons = [
    {
      heading: t("payrollService:whyPentabell:reasons:reason1:title"),
      text: t("payrollService:whyPentabell:reasons:reason1:description"),
    },
    {
      heading: t("payrollService:whyPentabell:reasons:reason2:title"),
      text: t("payrollService:whyPentabell:reasons:reason2:description"),
    },
    {
      heading: t("payrollService:whyPentabell:reasons:reason3:title"),
      text: t("payrollService:whyPentabell:reasons:reason3:description"),
    },
    {
      heading: t("payrollService:whyPentabell:reasons:reason4:title"),
      text: t("payrollService:whyPentabell:reasons:reason4:description"),
    },
  ];
  return (
    <div>
      <BannerComponents
        title={t("payrollService:intro:title")}
        description={t("payrollService:intro:description")}
        bannerImg={banner}
        altImg={t("payrollService:intro:altImg")}
        height={"70vh"}
        link={locale === "en" ? "/contact/" : `/${locale}/contact/`}
        linkTitle={t("global:contactUs")}
      />

      <ResponsiveRowTitleText
        title={t("payrollService:overview:title")}
        paragraph={t("payrollService:overview:paragraph1")}
        paragraph2={t("payrollService:overview:paragraph2")}
      />

      <p className="heading-h1 text-center text-banking">
        {t("payrollService:howWeHelp")}
      </p>

      <ServiceRow data={dataS1} />
      <ServiceRow data={dataS2} reverse={true} darkBg={true} />
      <ServiceRow data={dataS3} />
      <ApproachToPayroll locale={locale} />
      <HowItWorks locale={locale} steps={steps} />
      {/* <CTAPayroll
          title={"Ready For your next hire with Pentabell ?"}
          description={
            "Lorem ipsum dolor sit amet consectetur. Augue viverra mauris senectus id nulla neque volutpat amet in. Duis mauris augue vitae amet donec aliquet placerat porttitor. In."
          }
          btnLink={"#service-page-form"}
          btnText={"Submit"}
          img={ctaPayrollBg.src}
        /> */}
      <WhyPentabell locale={locale} reasons={reasons} />
      <AskQuestions
        title={t("payrollService:questions:title")}
        ASK_QUESTIONS={ASK_QUESTIONS}
      />
      {/* <InsightsSection /> */}
      <ServicePageForm />
    </div>
  );
}
export default payrollService;
