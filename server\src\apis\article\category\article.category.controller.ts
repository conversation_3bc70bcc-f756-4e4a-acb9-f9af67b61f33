import { Router, Request, Response, NextFunction } from 'express';
import multer from 'multer';

import CategoryService from './article.category.service';
import { Language } from '../article.interface';
import { CategoryVersion } from './article.category.interfaces';
import apiKeyMiddleware from '@/middlewares/validateApiKey.middleware';
import { validateMongoIds } from '../../../middlewares/mongoId-validation.middleware';
import HttpException from '@/utils/exceptions/http.exception';
import isAuthenticated from '@/middlewares/authentication.middleware';
import { hasRoles } from '@/middlewares/authorization.middleware';
import { invalidateCache, validateCache } from '@/middlewares/cache.middleware';
import { Role } from '@/utils/helpers/constants';
import { UserI } from '@/apis/user/user.interfaces';
import { MESSAGES } from '@/utils/helpers/messages';

class CategoryController {
    public readonly path = '/categories';
    private readonly categoryService = new CategoryService();
    public readonly router = Router();
    private upload: any = multer();

    constructor() {
        this.initialiseRoutes();
    }
    private initialiseRoutes(): void {
        this.router.get(`${this.path}`, apiKeyMiddleware, validateCache, this.getAllCategories);
        this.router.get(`/:language${this.path}`, apiKeyMiddleware, this.getAllCategoriesList);
        this.router.get(
            `${this.path}/:categoryId`,
            apiKeyMiddleware,
            isAuthenticated,
            hasRoles([Role.ADMIN, Role.EDITEUR]),
            validateMongoIds,
            validateCache,
            this.getCategory,
        );
        this.router.get(
            `${this.path}/:language/all`,
            apiKeyMiddleware,
            isAuthenticated,
            hasRoles([Role.ADMIN, Role.EDITEUR]),
            this.getCategoriesByLanguage,
        );
        this.router.get(
            `${this.path}/:language/:versionIds`,
            apiKeyMiddleware,
            isAuthenticated,
            hasRoles([Role.ADMIN, Role.EDITEUR]),
            validateCache,
            this.getOppositeLanguageVersions,
        );
        this.router.get(`${this.path}/:language/blog/:url`, apiKeyMiddleware, validateCache, this.getSlugBySlug);
        this.router.get(`${this.path}/:language/category/:url`, apiKeyMiddleware, this.getCategoryByUrl);

        this.router.post(`${this.path}`, isAuthenticated, hasRoles([Role.ADMIN, Role.EDITEUR]), invalidateCache, this.createCategory);
        this.router.post(
            `${this.path}/:language/:categoryId`,
            isAuthenticated,
            hasRoles([Role.ADMIN, Role.EDITEUR]),
            invalidateCache,
            this.upsertCategoryVersion,
        );
        this.router.delete(
            `${this.path}/:language/:categoryId`,
            isAuthenticated,
            hasRoles([Role.ADMIN, Role.EDITEUR]),
            invalidateCache,
            this.deleteCategoryByLanguageAndId,
        );
        this.router.put(`${this.path}/`, isAuthenticated, this.upload.single('file'), this.convertToNewModel);
    }

    private createCategory = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const categoryData = request.body;
            const newCategory = await this.categoryService.createCategory(categoryData);
            response.send({ category: newCategory });
        } catch (error) {
            next(error);
        }
    };

    private upsertCategoryVersion = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { categoryId, language } = req.params;
            const versionData: CategoryVersion = req.body;
            if (!categoryId || !language) return next(new HttpException(400, MESSAGES.CATEGORY.MISSING_ID_OR_LANGUAGE));
            const languageEnum = language as Language;
            const updatedCategory = await this.categoryService.upsertCategoryVersion(categoryId, languageEnum, versionData);
            res.send(updatedCategory);
        } catch (error) {
            next(error);
        }
    };

    private getAllCategoriesList = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { language } = req.params;
            const queries: any = req.query;
            const categories = await this.categoryService.getAllCategoriesList({ ...queries, language });
            res.send(categories);
        } catch (error) {
            next(error);
        }
    };

    private getAllCategories = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const queries: any = req.query;
            const categories = await this.categoryService.getAllCategories(queries);
            res.send(categories);
        } catch (error) {
            next(error);
        }
    };

    private getCategory = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const id: string = request.params.categoryId;
            const result = await this.categoryService.getCategory(id);
            response.send(result);
        } catch (error) {
            next(error);
        }
    };

    private getSlugBySlug = async (req: Request, res: Response, next: NextFunction) => {
        const { url, language } = req.params;
        try {
            const category = await this.categoryService.getSlugBySlug(language, url);
            res.send(category);
        } catch (error) {
            next(error);
        }
    };

    private getCategoryByUrl = async (req: any, res: Response, next: NextFunction) => {
        try {
            const { url, language } = req.params;
            const currentUser = req.user as UserI;
            const articles = await this.categoryService.getCategoryByUrl(language, url, currentUser);
            res.send(articles);
        } catch (error) {
            next(error);
        }
    };

    private getCategoriesByLanguage = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const { language } = request.params;
            const categories = await this.categoryService.getCategoriesByLanguage(language);

            response.send({ categories });
        } catch (error) {
            next(error);
        }
    };

    private deleteCategoryByLanguageAndId = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { language, categoryId } = req.params;
            if (!language || !categoryId) return next(new HttpException(400, MESSAGES.CATEGORY.MISSING_ID_OR_LANGUAGE));

            const deletedcategory = await this.categoryService.deleteCategoryByLanguageAndId(language as Language, categoryId);

            res.send({
                message: `Version with language ${language} deleted successfully from category with ID ${categoryId}.`,
                deletedcategory,
            });
        } catch (error) {
            next(error);
        }
    };

    private getOppositeLanguageVersions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { language, versionIds } = req.params;

            if (!language || !versionIds) return next(new HttpException(400, MESSAGES.CATEGORY.MISSING_ID_OR_LANGUAGE));

            const versionIdsArray = versionIds.split(',');
            const result = await this.categoryService.getOppositeLanguageVersionsCategory(language, versionIdsArray);
            res.send(result);
        } catch (error) {
            next(error);
        }
    };

    private convertToNewModel = async (req: Request, res: Response, next: NextFunction) => {
        try {
            if (!req.file) return res.status(400).json({ message: 'No file uploaded' });

            const file = req.file;
            const result = await this.categoryService.convertToNewModel(file);
            res.status(201).json({ convertedCount: result.length, categories: result });
        } catch (error: any) {
            console.error(error);
            res.status(500).json({ message: 'Conversion failed', error: error.message });
        }
    };
}
export default CategoryController;
