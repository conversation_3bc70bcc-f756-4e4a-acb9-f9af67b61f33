import { Language, robotsMeta } from '@/utils/helpers/constants';
import { ArticleI } from '../article.interface';

import mongoose from 'mongoose';
import { GuideI } from '@/apis/guide/guide.interface';

export interface CategoryVersion {
    language: Language;
    name: string;
    url: string;
    description: string;
    _id: any;
    image: string | null;
    articles: mongoose.Types.ObjectId[];
    guides: mongoose.Types.ObjectId[];
    metaTitle: string;
    metaDescription: string;
    canonical: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface CategoryI {
    _id: string;
    versions: { [language: string]: CategoryVersion };
    guides: mongoose.Types.ObjectId[];
    robotsMeta: robotsMeta;
    url: string;
    name: string;
    articles: mongoose.Types.ObjectId[];
}
export interface categorywithArticles {
    _id: string;
    name: string;
    description: string;
    articles: ArticleI[];
    guides: GuideI[];
}
