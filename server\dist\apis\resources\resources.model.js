"use strict";
// Filename: src/models/resource.schema.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResourceType = void 0;
const mongoose_1 = require("mongoose");
const constants_1 = require("@/utils/helpers/constants");
// An enum to define the possible types of resources
exports.ResourceType = {
    Article: 'Article',
    Guide: 'Guide',
    Event: 'Event',
};
const faqItemSchema = new mongoose_1.Schema({
    question: { type: String, required: true },
    answer: { type: String, required: true },
}, { _id: false });
/**
 * A single, merged version schema to hold fields from all possible resource types.
 * Fields not relevant to a specific type will be null or undefined.
 */
const resourceVersionSchema = new mongoose_1.Schema({
    // --- Common Fields ---
    language: { type: String, enum: constants_1.Language, required: true },
    title: { type: String, required: true },
    keywords: { type: [String] },
    metaTitle: { type: String, default: '' },
    metaDescription: { type: String, default: '' },
    content: { type: String },
    canonical: { type: String },
    visibility: { type: String, enum: constants_1.Visibility, default: constants_1.Visibility.Draft, required: true },
    publishDate: { type: Date },
    isArchived: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
    // --- Article & Guide Specific ---
    description: { type: String },
    highlights: { type: [String] },
    // --- Article Specific ---
    url: { type: String, trim: true }, // URL for articles
    faqTitle: { type: String, default: '' },
    faq: { type: [faqItemSchema], default: [] },
    category: [{ type: mongoose_1.Types.ObjectId, ref: 'Category' }],
    // --- Guide Specific ---
    alt: { type: String, default: '' },
    image: { type: String },
    file: { type: String },
    service: [{ type: mongoose_1.Types.ObjectId, ref: 'Category' }],
    guideList: { /* ... */},
    // --- Event Specific ---
    slug: { type: String, trim: true }, // Slug for events
    name: { type: String },
    subTitle: { type: String },
    sector: { type: String },
    countryConcerned: { type: String },
    organiser: { type: String },
    // --- Article & Event Specific ---
    shareOnSocialMedia: { type: Boolean, default: false },
});
const resourceSchema = new mongoose_1.Schema({
    // The discriminator key
    resourceType: {
        type: String,
        enum: Object.values(exports.ResourceType),
        required: true,
        index: true, // Crucial for performance
    },
    versions: { type: [resourceVersionSchema] },
    createdBy: { type: mongoose_1.Types.ObjectId, ref: 'User' },
    robotsMeta: { type: String, enum: constants_1.robotsMeta, default: constants_1.robotsMeta.index },
    // --- Fields from Parent Schemas ---
    tags: { type: [String] }, // Article
    totalCommentaires: { type: Number, default: 0 }, // Article
    isArticle: { type: Boolean }, // Guide
    cible: { type: String, enum: constants_1.Cible }, // Guide
    totalDownload: { type: Number, default: 0 }, // Guide
    mailBanner: { type: String }, // Guide
    country: { type: String, default: 'Tunisia' }, // Event
    startDate: { type: Date }, // Event
    endDate: { type: Date }, // Event
    mainImage: { desktop: String, mobile: String, alt: String }, // Event
    imageList: [String], // Event
}, {
    timestamps: true,
    toJSON: { transform: (doc, ret) => { delete ret.__v; } },
});
// --- Complex Validation Example ---
// Enforce that `startDate` is required ONLY IF the resource is an 'Event'
resourceSchema.pre('save', function (next) {
    if (this.resourceType === exports.ResourceType.Event && !this.startDate) {
        return next(new Error('Start date is required for resources of type Event.'));
    }
    next();
});
// --- INDEXES ---
// A single, powerful text index
resourceSchema.index({ 'versions.title': 'text', 'versions.description': 'text', 'versions.keywords': 'text', 'versions.sector': 'text' }, { name: 'ResourceTextIndex' });
// A sparse index on startDate, only indexes documents where the field exists
resourceSchema.index({ startDate: 1 }, { sparse: true });
exports.default = (0, mongoose_1.model)('Resource', resourceSchema);
//# sourceMappingURL=resources.model.js.map